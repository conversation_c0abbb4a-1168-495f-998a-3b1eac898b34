
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import { showSuccessToast, showErrorToast } from "@/core";
import { supabase } from '@/core/api/supabase';
import { fetchArtists } from '@/features/entities/api';
import { getUserEntityIdForBooking } from '@/features/entities/api/profileService';
import { useAuth } from '@/contexts/AuthContext';

// Import form section components
import {
  GeneralInformation,
  DateTimeSelection,
  ArtistSelection,
  ClientSelection,
  FinancialDetails
} from '@/features/bookings/components/form-sections';

import { ArtistData } from '@/features/entities/types';

interface Booking {
  id: string;
  title: string;
  venue_id: string;
  artist_id: string;
  booking_start: string;
  booking_end: string;
  description: string | null;
  pricing_type: 'fixed' | 'hourly';
  price: number;
  status: string;
  location: string;
  artist_name?: string;
  venue_name?: string;
  client_id?: string | null;
}

interface EditBookingModalProps {
  open: boolean;
  onClose: () => void;
  booking: Booking | null;
  userType: 'venue' | 'artist' | 'agency';
  onBookingUpdated?: () => void;
}

const EditBookingModal = ({
  open,
  onClose,
  booking,
  userType,
  onBookingUpdated
}: EditBookingModalProps) => {
  const { profile } = useAuth();

  // Form state - matching new booking modal structure
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [bookingType, setBookingType] = useState<'fixed' | 'hourly'>('fixed');
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);

  // Date and time state
  const [eventDate, setEventDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [startTime, setStartTime] = useState('18:00');
  const [endTime, setEndTime] = useState('22:00');
  const [validationErrors, setValidationErrors] = useState<{dates?: string}>({});
  const [totalPrice, setTotalPrice] = useState<number | null>(null);

  // Artist state
  const [artists, setArtists] = useState<ArtistData[]>([]);
  const [agencyArtists, setAgencyArtists] = useState<ArtistData[]>([]);
  const [selectedArtist, setSelectedArtist] = useState<ArtistData | null>(null);
  const [isLoadingArtists, setIsLoadingArtists] = useState(false);

  // Client state for agencies
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  // Venue state
  const [venueId, setVenueId] = useState<string | null>(null);

  // Initialize form data when modal opens
  useEffect(() => {
    if (open && booking) {
      const startDate = new Date(booking.booking_start);
      const endDate = new Date(booking.booking_end);

      setTitle(booking.title);
      setLocation(booking.location);
      setDescription(booking.description || '');
      setBookingType(booking.pricing_type);
      setPrice(String(booking.price));
      setVenueId(booking.venue_id);
      setEventDate(startDate);
      setEndDate(endDate);
      setStartTime(startDate.toTimeString().slice(0, 5));
      setEndTime(endDate.toTimeString().slice(0, 5));
      setSelectedClientId(booking.client_id || null);

      loadArtists();
    }
  }, [open, booking]);

  // Load artists using shared function
  const loadArtists = async () => {
    try {
      setIsLoadingArtists(true);
      const artistsData = await fetchArtists();
      setArtists(artistsData);

      // If user is from an agency, fetch their artists
      if (userType === 'agency') {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          const { data: entityData } = await supabase
            .from('entity_users')
            .select('entity_id')
            .eq('user_id', user.id)
            .single();

          if (entityData?.entity_id) {
            const { data: agencyArtistsData } = await supabase
              .from('agency_with_artists')
              .select('*')
              .eq('agency_id', entityData.entity_id);

            if (agencyArtistsData && agencyArtistsData.length > 0) {
              const formattedAgencyArtists = agencyArtistsData.map(artist => ({
                id: artist.artist_entity_id,
                artist_name: artist.artist_name,
                name: artist.artist_name,
                profile_image_url: artist.artist_profile_image,
                genre: artist.artist_genre,
                region: artist.artist_region
              }));
              setAgencyArtists(formattedAgencyArtists);
            }
          }
        } catch (error) {
          console.error('Error fetching agency artists:', error);
        }
      }
    } catch (error) {
      console.error('Error loading artists:', error);
      showErrorToast('Failed to load artists');
    } finally {
      setIsLoadingArtists(false);
    }
  };

  // Set selected artist when artists are loaded
  useEffect(() => {
    if (selectedArtist === null && booking && artists.length > 0) {
      const bookingArtist = artists.find(artist => artist.id === booking.artist_id);
      if (bookingArtist) {
        setSelectedArtist(bookingArtist);
      }
    }
  }, [artists, booking, selectedArtist]);

  // Validation and submit logic
  const formatTimeForDatabase = (date: Date, timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const validateBookingData = () => {
    if (validationErrors.dates) {
      showErrorToast(validationErrors.dates);
      return false;
    }

    if (!selectedArtist) {
      showErrorToast("Please select an artist");
      return false;
    }

    if (!title || !location) {
      showErrorToast('Please fill in all required fields');
      return false;
    }

    if (!price || parseFloat(price) <= 0) {
      showErrorToast('Please enter a valid price');
      return false;
    }

    if (!eventDate || !startTime || !endTime) {
      showErrorToast('Please fill in all date and time fields');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!booking || !validateBookingData()) return;

    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        showErrorToast("You must be logged in to update a booking");
        return;
      }

      const userEntityId = await getUserEntityIdForBooking(user.id, userType);
      const bookingStart = formatTimeForDatabase(eventDate, startTime);
      const bookingEnd = formatTimeForDatabase(endDate, endTime);

      const updateData: any = {
        title,
        venue_id: venueId || userEntityId,
        artist_id: selectedArtist!.id,
        booking_start: bookingStart.toISOString(),
        booking_end: bookingEnd.toISOString(),
        description,
        pricing_type: bookingType,
        price: parseFloat(price),
        location,
        owner_entity_id: venueId || userEntityId
      };

      // Add client_id for agency bookings
      if (userType === 'agency') {
        updateData.client_id = selectedClientId;
      }

      const { error } = await supabase
        .from('bookings')
        .update(updateData)
        .eq('id', booking.id);

      if (error) throw error;

      showSuccessToast('Booking updated successfully!');
      if (onBookingUpdated) onBookingUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating booking:', error);
      showErrorToast('Failed to update booking');
    } finally {
      setLoading(false);
    }
  };

  if (!booking) return null;

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Booking</DialogTitle>
          <DialogDescription>Update the details for this booking.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* General Information Section */}
          <GeneralInformation
            title={title}
            setTitle={setTitle}
            location={location}
            setLocation={setLocation}
            description={description}
            setDescription={setDescription}
          />

          {/* Date and Time Section */}
          <DateTimeSelection
            eventDate={eventDate}
            setEventDate={setEventDate}
            endDate={endDate}
            setEndDate={setEndDate}
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            validationErrors={validationErrors}
            setValidationErrors={setValidationErrors}
          />

          {/* Artist Selection Section */}
          <h3 className="text-lg font-semibold">Artist</h3>
          <ArtistSelection
            selectedArtist={selectedArtist}
            setSelectedArtist={setSelectedArtist}
            artists={artists}
            agencyArtists={agencyArtists}
            userType={userType}
            isLoadingArtists={isLoadingArtists}
          />

          {/* Client Selection Section (only for agencies) */}
          <ClientSelection
            selectedClientId={selectedClientId}
            setSelectedClientId={setSelectedClientId}
            userType={userType}
          />

          {/* Financial Details Section */}
          <FinancialDetails
            bookingType={bookingType}
            setBookingType={setBookingType}
            price={price}
            setPrice={setPrice}
            eventDate={eventDate}
            endDate={endDate}
            startTime={startTime}
            endTime={endTime}
            totalPrice={totalPrice}
            setTotalPrice={setTotalPrice}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !!validationErrors.dates}
            className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-zinc-950 hover:bg-zinc-800"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Booking'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBookingModal;
